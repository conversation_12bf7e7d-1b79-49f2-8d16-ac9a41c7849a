<template>
    <div class="key-statistics" :class="{ 'collapsed': !isExpanded }">
        <!-- Loading state -->
        <div v-if="loading" class="statistics-loading">
            <div class="loading-text">Loading statistics...</div>
        </div>

        <!-- Statistics data (always show, display zero values on error) -->
        <div v-else class="statistics-data">
            <div class="stat-item">
                <div class="stat-label">Total<span v-if="!isExpanded">:</span></div>
                <div class="stat-value" :class="{ 'value-updated': valueUpdated }">
                    {{ formatNumber(statistics.total_keys || 0) }}
                </div>
            </div>

            <div class="stat-divider"></div>

            <div class="stat-item">
                <div class="stat-label">Available<span v-if="!isExpanded">:</span></div>
                <div class="stat-value" :class="{ 'value-updated': valueUpdated }">
                    {{ formatNumber(statistics.available || 0) }}
                </div>
            </div>

            <div class="stat-divider"></div>

            <div class="stat-item">
                <div class="stat-label">Checked Out<span v-if="!isExpanded">:</span></div>
                <div class="stat-value" :class="{ 'value-updated': valueUpdated }">
                    {{ formatNumber(statistics.checked_out || 0) }}
                </div>
            </div>

            <div class="stat-divider"></div>

            <div class="stat-item">
                <div class="stat-label">Lost<span v-if="!isExpanded">:</span></div>
                <div class="stat-value" :class="{ 'value-updated': valueUpdated }">
                    {{ formatNumber(statistics.lost || 0) }}
                </div>
            </div>

            <div class="stat-divider"></div>

            <div class="stat-item">
                <div class="stat-label">Damaged<span v-if="!isExpanded">:</span></div>
                <div class="stat-value" :class="{ 'value-updated': valueUpdated }">
                    {{ formatNumber(statistics.damaged || 0) }}
                </div>
            </div>

            <div class="stat-divider"></div>

            <div class="stat-item">
                <div class="stat-label">Archived<span v-if="!isExpanded">:</span></div>
                <div class="stat-value" :class="{ 'value-updated': valueUpdated }">
                    {{ formatNumber(statistics.archived || 0) }}
                </div>
            </div>
        </div>

        <div class="icon-area" @click="toggleExpanded">
            <i class="icon2017" :class="isExpanded ? 'icon-expand_up_02' : 'icon-expand_down_02'"></i>
        </div>
    </div>
</template>

<script>
import keyApi from '../api.js';

export default {
    name: 'KeyStatistics',
    // Remove props as statistics should not be linked to filters
    data() {
        return {
            isExpanded: true,
            statistics: {
                total_keys: 0,
                available: 0,
                checked_out: 0,
                lost: 0,
                damaged: 0,
                archived: 0
            },
            loading: false,
            valueUpdated: false
        };
    },
    mounted() {
        // Load statistics data only once when component is mounted
        this.loadStatistics();
    },
    methods: {
        /**
         * Load statistics data from API
         */
        async loadStatistics() {
            this.loading = true;

            try {
                // Call API without any filters (get overall statistics)
                const response = await keyApi.getKeyStatistics();

                if (response.success) {
                    // Check if data has changed to trigger update animation
                    const hasChanged = JSON.stringify(this.statistics) !== JSON.stringify(response.data);
                    this.statistics = response.data;

                    if (hasChanged) {
                        this.triggerUpdateAnimation();
                    }
                } else {
                    // On error, keep zero values (don't show error message)
                    console.warn('Failed to load statistics:', response.error);
                }
            } catch (error) {
                // On error, keep zero values (don't show error message)
                console.error('Failed to load statistics:', error);
            } finally {
                this.loading = false;
            }
        },

        /**
         * Toggle expand/collapse state
         */
        toggleExpanded() {
            this.isExpanded = !this.isExpanded;
        },

        /**
         * Trigger value update animation
         */
        triggerUpdateAnimation() {
            this.valueUpdated = true;
            setTimeout(() => {
                this.valueUpdated = false;
            }, 600);
        },

        /**
         * Format number display
         */
        formatNumber(num) {
            if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'k';
            }
            return num.toString();
        }
    }
};
</script>

<style scoped>
.key-statistics {
    display: flex;
    gap: 20px;
    padding: 0 0 0 20px;
    width: 100%; 
    background: #FFFFFF;
    border: 1px solid #EBECF1;
    border-radius: 6px;
    transition: all 0.3s ease;
    min-height: 80px;
}

/* Loading state styles */
.statistics-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    padding: 20px;
}

.loading-text {
    font-family: 'SF Pro Text';
    font-size: 14px;
    color: #797E8B;
}

.statistics-data {
    display: flex;
    align-items: center;
    flex: 1;
    gap: 20px;
    padding: 20px 0;
    transition: padding 0.3s ease;
}

/* 折叠状态 */
.key-statistics.collapsed .statistics-data {
    padding: 15px 0;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 10px;
    flex: 1;
    transition: all 0.3s ease;
}

/* 折叠状态下的水平布局 */
.key-statistics.collapsed .stat-item {
    flex-direction: row;
    align-items: center;
    gap: 5px;
}

.stat-label {
    font-family: 'SF Pro Text';
    font-weight: 500;
    font-size: 14px;
    line-height: 1.43;
    color: #797E8B;
    transition: all 0.3s ease;
}

.stat-value {
    font-family: 'SF Pro Text';
    font-weight: 700;
    font-size: 22px;
    line-height: 1.36;
    color: #202437;
    transition: all 0.3s ease;
}

/* 数值更新动画 */
.stat-value.value-updated {
    animation: valueUpdate 0.6s ease-in-out;
}

@keyframes valueUpdate {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
        color: #5D51E2;
    }
    100% {
        transform: scale(1);
    }
}

/* 不同状态的颜色 */
.stat-available {
    color: #20C472;
}

.stat-checked-out {
    color: #5D51E2;
}

.stat-lost {
    color: #F0454C;
}

.stat-damaged {
    color: #F0454C;
}

.stat-archived {
    color: #C6C8D1;
}

/* 折叠状态下的较小字体 */
.key-statistics.collapsed .stat-value {
    font-size: 14px;
    line-height: 1.43;
}

.stat-divider {
    width: 0;
    height: 100%;
    border-left: 1px solid #EBECF1;
    align-self: stretch;
}

.icon-area {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    background: rgba(93, 81, 226, 0.1);
    align-self: stretch;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.icon-area:hover {
    background: rgba(93, 81, 226, 0.15);
}

.icon-area .icon2017 {
    width: 10px;
    height: 10px;
    color: #5D51E2;
    font-size: 10px;
    transition: transform 0.3s ease;
}

/* 图标旋转动画 */
.key-statistics.collapsed .icon-area .icon2017 {
    transform: rotate(180deg);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .key-statistics {
        padding: 0 0 0 15px;
    }

    .statistics-data {
        gap: 15px;
    }

    .stat-item {
        min-width: 80px;
    }

    .stat-value {
        font-size: 18px;
    }

    .key-statistics.collapsed .stat-value {
        font-size: 12px;
    }
}

@media (max-width: 768px) {
    .key-statistics {
        padding: 0 0 0 10px;
    }

    .statistics-data {
        gap: 10px;
        padding: 15px 0;
    }

    .stat-item {
        min-width: 60px;
    }

    .stat-label {
        font-size: 12px;
    }

    .stat-value {
        font-size: 16px;
    }

    .key-statistics.collapsed .stat-value {
        font-size: 11px;
    }
}
</style>
