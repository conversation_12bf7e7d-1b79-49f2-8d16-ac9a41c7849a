/* eslint-disable prettier/prettier */
import permission from "./permission";
import blackList from "./blackList";
import { globalization } from "common";
import { localStore, crmUtils, infoData, basePermission } from "crm";
import qs from "qs";
import { useGlobals } from "@/hooks";

const { isUK } = useGlobals();

const listPages = ["all", "leadpond", "trash", "partial", "search"];
const newSettingPages = [
    "profile",
    "preference",
    "agent",
    "companyProfile",
    "office",
    "lender",
    "customField",
    "autoAlert",
    "leadRouting",
    "assignGroupMgt",
    "leadPond",
    "vendor",
    "smartPlan",
    "openHouse",
    "import",
    "export",
    "textCode",
    "billing",
    "templateSettings",
    "companyNumber",
    "appCenter",
    "manageDialer",
    "leadCapture",
    "emailTemplate",
    "manageDomain",
    "reporting"
];
let oldSettingPages = ["tags", "agentPartner"];

// const testUrl = "reporting";

/**
 *  VueRouter Configuration
 *  ***( page name ): {
 * 		static: // need to execute js method
		bodyClass: //body labeled class
		title: // page title
		getPageName: // get page name Methods
        hideChat: // hide chat ， default false，true hide
        noHeader: //  Indicates whether to hide the navigation bar
        headerMenuName: //  corresponding top header menu of name
        hideSideMenu: //  Hide the left menu bar ， default false，true hide
    }
 */
const ListingManagementComponent = () =>
    import(
        /* webpackPrefetch: true */ `../listing-management${
            isUK || permission.useSearchCenter() ? "-v2" : ""
        }/app.vue`
    );

export default function (compLoadedCallback) {
    //  internationalization module hour ， The first parameter is a function () => import("")
    function loadComponent(psComp, langModule) {
        var ps = psComp;
        if (typeof psComp === "function" && langModule) {
            ps = loadLangModule(langModule).then(psComp);
        }
        return ps.then((comp) => {
            if (compLoadedCallback) {
                compLoadedCallback();
            }
            return comp;
        });
    }

    function loadLangModule(module) {
        return globalization.loadModule(module).catch(console.error);
    }

    const { isLofty } = infoData.getUserInfo();

    const { isNewObDashboard, isNewObUser } = infoData;

    const { LoftyChat, ReportNewView } = basePermission;

    const path = isLofty && !LoftyChat ? "/showing/list" : "";
    let homeLanguagLst = ["homepage", "commonModule"];
    if (isNewObUser) {
        homeLanguagLst.push(
            "homepage",
            "obDashboard",
            "task",
            "campaigns-socialStudio",
            "setting-preference"
        );
    }
    return [
        {
            path: "/",
            redirect: "/home"
        },
        {
            name: "home",
            path: "/home",
            redirect: path,
            meta: {
                bodyClass: "home",
                title: isLofty ? "Chat" : "LoftyWorks",
                headerMenuName: "home",
                oncePop: !isNewObDashboard
            },
            component: () =>
                loadComponent(() => {
                    // Check if it is a lofty account
                    if (isLofty) {
                        return import(/* webpackPrefetch: true */ "../loftyChat/index.vue");
                    } else if (infoData.isNewObDashboard) {
                        return import(/* webpackPrefetch: true */ "../homepage/index.vue");
                    } else {
                        return import(/* webpackPrefetch: true */ "../homepage/index.vue");
                    }
                }, homeLanguagLst),
            beforeEnter(to, from, next) {
                if (to.hash.startsWith("#/")) {
                    const _qs = qs.parse(to.hash.match(/\?(.*)/)?.[1] ?? "");
                    next({
                        path: to.hash.substr(1),
                        query: Object.assign(_qs, to.query),
                        params: to.params
                    });
                } else {
                    if (infoData.isNewObDashboard && infoData.isOBVThree) {
                        next("/obDashboardV3");
                    } else {
                        next();
                    }
                }
            }
        },
        {
            name: "obDashboardV3",
            path: "/obDashboardV3",
            meta: {
                bodyClass: "home",
                title: "LoftyWorks",
                headerMenuName: "home",
                oncePop: false,
                hideChat: true,
                authority: () => {
                    if (!infoData.isOBVThree) {
                        return { name: "home" };
                    }
                }
            },
            component: () =>
                loadComponent(() => import("../obDashboardV3/index.vue"), homeLanguagLst)
        },
        {
            name: "training",
            path: "/get-training",
            meta: {
                bodyClass: "home",
                title: "LoftyWorks",
                headerMenuName: "home",
                oncePop: false,
                hideChat: true
            },
            component: () =>
                loadComponent(() => import("../obDashboardV3/trainingPage.vue"), ["obDashboard"])
        },
        {
            name: "suggestFeatures",
            path: "/suggest-features",
            meta: {
                title: "Suggest Features",
                hideChat: true,
                oncePop: false
                //authority: permission.referral
            },
            component: () => loadComponent(import("../suggestFeatures/index.vue"))
        },
        {
            name: "referral",
            path: "/referral",
            meta: {
                title: "Referral campaign",
                hideChat: true,
                oncePop: false
                //authority: permission.referral
            },
            component: () => loadComponent(import("../referral/index.vue"))
        },
        {
            name: "tc",
            path: "/tc",
            meta: {
                title: "Referral Program Terms and Conditions",
                noHeader: true,
                hideChat: true
            },
            component: () => loadComponent(import("../referral/tc.vue"))
        },
        {
            name: "learning-system-center",
            path: "/learning-system-center",
            meta: {
                bodyClass: "learning-system",
                title: "LoftyWorks Learning Center"
            },
            component: () =>
                loadComponent(() => import("../learn-system/center/index.vue"), "learnSystem")
        },
        {
            name: "learning-system-detail",
            path: "/learning-system-detail/:id",
            meta: {
                bodyClass: "learning-system",
                title: "LoftyWorks Learning Center"
            },
            component: () =>
                loadComponent(() => import("../learn-system/detail/index.vue"), "learnSystem")
        },
        {
            // partialLead  old page compatible
            path: "/lead/list",
            redirect: (to) => {
                if (isLofty) {
                    return { name: "freeLeadList" };
                }

                return { name: "leadList" };
            }
        },
        {
            path: "/lead/partialList",
            redirect: () => {
                return { name: "leadList", query: { type: "partial" } };
            }
        },
        {
            name: "partialList",
            path: "/lead/partialList",
            meta: {
                bodyClass: "people",
                title: "Leads | LoftyWorks",
                getPageName: () => "list_partial",
                authority: permission.checkAuth.bind(
                    null,
                    ({ userInfo, basePermission }) => {
                        return userInfo.teamType === 0 && basePermission.Lead;
                    },
                    { name: "home" }
                ),
                headerMenuName: "people"
            },
            component: () =>
                loadComponent(import(`../list-v2/components/PartialLeads/partialLeads.vue`))
        },
        {
            name: "segments",
            path: "/lead/segments",
            meta: {
                bodyClass: "people",
                title: "Segment | LoftyWorks",
                getPageName: () => "list_segments",
                authority: permission.leadList,
                headerMenuName: "people"
            },
            component: () =>
                loadComponent(
                    () => import(`../segmentList/index.vue`),
                    ["leadList", "printCenter", "campaigns-designCenter", "geoFarming"]
                )
        },
        {
            name: "segmentDetail",
            path: "/lead/segmentDetail",
            meta: {
                bodyClass: "people",
                title: "Segment | LoftyWorks",
                getPageName: () => "list_segmentDetail",
                authority: permission.leadList,
                headerMenuName: "people"
            },
            component: () =>
                loadComponent(
                    () => import(`../segmentDetail/index.vue`),
                    [
                        "segmentDetail",
                        "leadList",
                        "printCenter",
                        "campaigns-designCenter",
                        "geoFarming"
                    ]
                )
        },
        {
            name: "leadList",
            path: "/lead/list",
            meta: {
                bodyClass: "people",
                title: "Leads | LoftyWorks",
                getPageName: (route) => {
                    return listPages.indexOf(route.query.type) === -1
                        ? "list_all"
                        : `list_${route.query.type}`;
                },
                authority: permission.leadList,
                headerMenuName: "people"
            },
            component: () => {
                return loadComponent(
                    () => import(/* webpackPrefetch: true */ `../list-v2/index.vue`),
                    ["leadList", "printCenter", "campaigns-designCenter"]
                );
            }
        },
        {
            name: "freeLeadList",
            path: "/lead/freeList",
            meta: {
                bodyClass: "people",
                title: "Leads | LoftyWorks",
                getPageName: () => {
                    return "list_free";
                },
                authority: () => {
                    if (!isLofty) {
                        return { name: "leadList" };
                    }
                },
                headerMenuName: "people"
            },
            component: () => {
                return loadComponent(() => import("../freeLeadList/index.vue"), ["leadList"]);
            }
        },
        {
            name: "ChatWindow",
            path: "/chatWindow",
            meta: {
                noHeader: false,
                title: "Message | LoftyWorks",
                hideChat: true
            },
            component: () => loadComponent(() => import("../chat-window/index.vue"), "chatWindow")
        },
        {
            name: "Workspace",
            path: "/workspace",
            meta: {
                noHeader: false,
                title: "Message | LoftyWorks",
                hideChat: true
            },
            component: () => loadComponent(() => import("../chat-window/index.vue"), "chatWindow")
        },
        {
            name: "leadDetail",
            path: "/lead/detail",
            redirect: { name: "detail" }
        },
        {
            name: "detail",
            path: "/detail",
            meta: {
                bodyClass: "people-detail",
                headerMenuName: "people"
            },
            component: () => {
                return loadComponent(
                    () => import(/* webpackPrefetch: true */ "../detail/app.vue"),
                    ["leadDetail", "printCenter", "campaigns-designCenter"]
                );
            }
        },
        {
            path: "/task",
            redirect: (route) => {
                const mode = localStore.getLocal("taskViewMode");
                const modeToRouterName = {
                    list: "taskManagement_list",
                    showing: "taskManagement_showing",
                    calendar: "taskManagement_calendar"
                };

                // For the lofty account, only the display of the task is currently developed, so the temporary interception is the display, which may be removed later.
                if (isLofty) {
                    return {
                        name: modeToRouterName.showing,
                        query: route.query
                    };
                }

                return {
                    name: modeToRouterName[mode] || modeToRouterName.calendar,
                    query: route.query
                };
            }
        },
        {
            name: "taskManagement_list",
            path: "/task/list",
            meta: {
                bodyClass: "tasks",
                title: "Tasks | LoftyWorks",
                headerMenuName: "task",
                authority: permission.task
            },
            component: () =>
                loadComponent(
                    () =>
                        import(
                            /* webpackChunkName: "js/taskmgmt",webpackPrefetch: true */ "../task-management/list.vue"
                        ),
                    "task"
                ),
            props: () => {
                return {
                    defMode: "list"
                };
            }
        },
        {
            path: "/showing/list",
            redirect: "/task/showing"
        },
        {
            name: "taskManagement_showing",
            path: "/task/showing",
            meta: {
                bodyClass: "tasks",
                title: isLofty ? "Showings | LoftyWorks" : "Tasks | LoftyWorks",
                headerMenuName: "task",
                hideChat: true,
                pageName: "Showings-Task"
            },
            component: () =>
                loadComponent(
                    () =>
                        import(
                            /* webpackChunkName: "js/taskmgmt-showing",webpackPrefetch: true */ "../task-management/showing/index.vue"
                        ),
                    ["commonModule", "task"]
                ),
            props: () => ({ isLofty })
        },
        {
            name: "taskManagement_calendar",
            path: "/task/calendar",
            meta: {
                bodyClass: "tasks",
                title: "Tasks | LoftyWorks",
                headerMenuName: "task",
                authority: permission.checkAuth.bind(
                    null,
                    ({ userInfo, basePermission }) => {
                        return userInfo.isInCalendarWhiteList && basePermission.Calendar;
                    },
                    { name: "taskManagement_list" }
                )
            },
            component: () =>
                loadComponent(
                    () =>
                        import(
                            /* webpackChunkName: "js/taskmgmt",webpackPrefetch: true */ "../task-management/app.vue"
                        ),
                    ["task", "printCenter"]
                ),
            props: () => {
                return {
                    defMode: "calendar"
                };
            }
        },
        {
            name: "activities",
            path: "/activities",
            meta: {
                bodyClass: "activities",
                title: "Activities | LoftyWorks",
                authority: permission.checkAuth.bind(
                    null,
                    ({ checkLimit }) => checkLimit("_activities"),
                    { name: "home" }
                ),
                headerMenuName: "activity"
            },
            component: () => loadComponent(() => import("../activities/index.vue"), "activities")
        },
        ReportNewView
            ? {
                name: "reporting",
                path: "/reporting",
                meta: {
                    title: "Reporting | Lofty",
                    bodyClass: "report",
                    getPageName: (route) => {
                        return `reporting_${route.query.page}`;
                    },
                    authority: [
                        permission.notLender.bind(null, { name: "home" }),
                        permission.reporting
                    ],
                    headerMenuName: "reporting"
                },
                component: () =>
                    loadComponent(
                        () => import("../reportingV2/index.vue"),
                        ["reporting", "reportingV2"]
                    ),
                children: [
                    {
                        name: "activities",
                        path: "activities",
                        meta: {
                            bodyClass: "activities",
                            title: "Activities | Lofty",
                            authority: permission.checkAuth.bind(
                                null,
                                ({ checkLimit }) => checkLimit("_activities"),
                                { name: "reporting" }
                            ),
                            headerMenuName: "activity",
                            byPassAuthority: true
                        },
                        component: () =>
                            loadComponent(() => import("../activities/index.vue"), "activities")
                    },
                    {
                        name: "customReport",
                        path: "customReport",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "customReport",
                            authority: [
                                permission.notLender.bind(null, { name: "home" }),
                                permission.reporting
                            ]
                        },
                        component: () =>
                            loadComponent(
                                () => import("../reportingV2/modules/customReport.vue"),
                                "reporting"
                            )
                    },

                    {
                        name: "isaAccountability",
                        path: "isaAccountability",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "isaAccountability",
                            authority: permission.checkAuth.bind(
                                null,
                                ({ basePermission, checkUserRight }) =>
                                    basePermission.LucidoReporting &&
                                      (checkUserRight("ISA_REPORT") ||
                                          checkUserRight("ISA_REPORT_DEPARTMENT")),
                                { name: "home" }
                            )
                        },
                        component: () =>
                            loadComponent(
                                () => import("../reporting/components/ISAAccountability.vue"),
                                "reporting"
                            )
                    },

                    {
                        name: "agentPipeline",
                        path: "agentPipeline",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "agentPipeline",
                            authority: permission.checkAuth.bind(
                                null,
                                ({ basePermission, checkUserRight }) =>
                                    basePermission.LucidoReporting &&
                                      (checkUserRight("AGENT_PIPELINE_REPORT") ||
                                          checkUserRight("AGENT_PIPELINE_REPORT_DEPARTMENT")),
                                { name: "home" }
                            )
                        },
                        component: () =>
                            loadComponent(
                                () => import("../reporting/components/AgentPipeline.vue"),
                                "reporting"
                            )
                    },
                    // {
                    //     name: "myAccountability",
                    //     path: "myAccountability",
                    //     redirect: "/reporting/agentReportCustom"
                    // },
                    {
                        name: "agentSummary",
                        path: "agentSummary",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "agentSummary",
                            authority: [
                                permission.notLender.bind(null, { name: "home" }),
                                permission.reporting
                            ]
                        },
                        component: () =>
                            loadComponent(
                                () => import("../reporting/components/agentSummary.vue"),
                                "reporting"
                            )
                    },
                    {
                        name: "myPerformance",
                        path: "myPerformance",
                        redirect: "/reporting/agentSummary"
                    },
                    {
                        name: "agentSourceIndex",
                        path: "agentSourceIndex",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "agentSourceIndex",
                            authority: [
                                permission.notLender.bind(null, { name: "home" }),
                                permission.reporting
                            ]
                        },
                        component: () =>
                            loadComponent(
                                () => import("../reporting/components/agentSourceIndex.vue"),
                                "reporting"
                            )
                    },
                    {
                        name: "goal_report",
                        path: "goalReport",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "goal_report",
                            authority: [
                                permission.notLender.bind(null, { name: "home" }),
                                permission.reporting
                            ]
                        },
                        component: () =>
                            loadComponent(
                                () => import("../reportingV2/modules/goalReport.vue"),
                                "reportingV2"
                            )
                    },
                    {
                        name: "teamOnboarding",
                        path: "teamOnboarding",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "teamOnboarding",
                            authority: permission.teamOb
                        },

                        component: () =>
                            loadComponent(
                                () => import("../reporting/components/teamOnboarding.vue"),
                                ["reporting", "obDashboard"]
                            )
                    },
                    {
                        name: "revenueShare",
                        path: "revenueShare",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "revenueShare",
                            authority: permission.revenueShare
                        },
                        component: () =>
                            loadComponent(
                                () => import("../reporting/components/RevenueShare.vue"),
                                ["reporting"]
                            )
                    },
                    {
                        name: "agentSource",
                        path: "agentSource",
                        redirect: "/reporting/agentSourceIndex"
                    },
                    {
                        name: "emailAccountability",
                        path: "emailAccountability",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "emailAccountability",
                            authority: [
                                permission.notLender.bind(null, { name: "home" }),
                                permission.reporting
                            ]
                        },
                        component: () =>
                            loadComponent(
                                () => import("../reporting/components/emailAccountability.vue"),
                                "reporting"
                            )
                    },
                    // 1
                    {
                        name: "agentPerformance",
                        path: "agentPerformance",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "agentPerformance",
                            authority: [
                                permission.notLender.bind(null, { name: "home" }),
                                permission.reporting
                            ]
                        },
                        component: () =>
                            loadComponent(
                                () => import("../reporting/components/agentPerformance.vue"),
                                "reporting"
                            )
                    },
                    {
                        name: "trafficReport",
                        path: "trafficReport",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "trafficReport",
                            byPassAuthority: true
                        },
                        component: () =>
                            loadComponent(
                                () => import("../reporting/components/trafficReport.vue"),
                                "reporting"
                            )
                    },
                    {
                        name: "agentSource",
                        path: "agentSource",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "agentSource",
                            byPassAuthority: true
                        },
                        component: () =>
                            loadComponent(
                                () => import("../reporting/components/agentSource.vue"),
                                "reporting"
                            )
                    },
                    {
                        name: "agentRecord",
                        path: "agentRecord",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "agentRecord",
                            authority: [permission.agentRecord]
                        },
                        component: () =>
                            loadComponent(
                                () => import("../reporting/components/agentRecord.vue"),
                                "reporting"
                            )
                    }
                ]
            }
            : {
                name: "reporting",
                path: "/reporting",
                meta: {
                    title: "Reporting | Lofty",
                    bodyClass: "report",
                    getPageName: (route) => {
                        return `reporting_${route.query.page}`;
                    },
                    authority: [
                        permission.notLender.bind(null, { name: "home" }),
                        permission.reporting
                    ],
                    headerMenuName: "reporting"
                },
                component: () => loadComponent(() => import("../reporting/app.vue"), "reporting"),
                children: [
                    {
                        name: "activities",
                        path: "activities",
                        meta: {
                            bodyClass: "activities",
                            title: "Activities | Lofty",
                            authority: permission.checkAuth.bind(
                                null,
                                ({ checkLimit }) => checkLimit("_activities"),
                                { name: "reporting" }
                            ),
                            headerMenuName: "activity",
                            byPassAuthority: true
                        },
                        component: () =>
                            loadComponent(() => import("../activities/index.vue"), "activities")
                    },
                    {
                        name: "agentAccountability",
                        path: "agentAccountability",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "agentAccountability",
                            authority: [
                                permission.notLender.bind(null, { name: "home" }),
                                permission.reporting
                            ]
                        },
                        component: () =>
                            loadComponent(
                                () => import("../reporting/components/agentAccountability.vue"),
                                "reporting"
                            )
                    },
                    {
                        name: "isaAccountability",
                        path: "isaAccountability",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "isaAccountability",
                            authority: permission.checkAuth.bind(
                                null,
                                ({ basePermission, checkUserRight }) =>
                                    basePermission.LucidoReporting &&
                                      (checkUserRight("ISA_REPORT") ||
                                          checkUserRight("ISA_REPORT_DEPARTMENT")),
                                { name: "home" }
                            )
                        },
                        component: () =>
                            loadComponent(
                                () => import("../reporting/components/ISAAccountability.vue"),
                                "reporting"
                            )
                    },
                    {
                        name: "agentPipeline",
                        path: "agentPipeline",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "agentPipeline",
                            authority: permission.checkAuth.bind(
                                null,
                                ({ basePermission, checkUserRight }) =>
                                    basePermission.LucidoReporting &&
                                      (checkUserRight("AGENT_PIPELINE_REPORT") ||
                                          checkUserRight("AGENT_PIPELINE_REPORT_DEPARTMENT")),
                                { name: "home" }
                            )
                        },
                        component: () =>
                            loadComponent(
                                () => import("../reporting/components/AgentPipeline.vue"),
                                "reporting"
                            )
                    },
                    {
                        name: "myAccountability",
                        path: "myAccountability",
                        redirect: "/reporting/agentAccountability"
                    },
                    {
                        name: "agentSummary",
                        path: "agentSummary",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "agentSummary",
                            authority: [
                                permission.notLender.bind(null, { name: "home" }),
                                permission.reporting
                            ]
                        },
                        component: () =>
                            loadComponent(
                                () => import("../reporting/components/agentSummary.vue"),
                                "reporting"
                            )
                    },
                    {
                        name: "myPerformance",
                        path: "myPerformance",
                        redirect: "/reporting/agentSummary"
                    },
                    {
                        name: "agentSourceIndex",
                        path: "agentSourceIndex",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "agentSourceIndex",
                            authority: [
                                permission.notLender.bind(null, { name: "home" }),
                                permission.reporting
                            ]
                        },
                        component: () =>
                            loadComponent(
                                () => import("../reporting/components/agentSourceIndex.vue"),
                                "reporting"
                            )
                    },
                    {
                        name: "teamOnboarding",
                        path: "teamOnboarding",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "teamOnboarding",
                            authority: permission.teamOb
                        },

                        component: () =>
                            loadComponent(
                                () => import("../reporting/components/teamOnboarding.vue"),
                                ["reporting", "obDashboard"]
                            )
                    },
                    {
                        name: "goal_report",
                        path: "goalReport",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "goal_report",
                            authority: [
                                permission.notLender.bind(null, { name: "home" }),
                                permission.reporting
                            ]
                        },
                        component: () =>
                            loadComponent(
                                () => import("../reportingV2/modules/goalReport.vue"),
                                "reportingV2"
                            )
                    },
                    {
                        name: "revenueShare",
                        path: "revenueShare",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "revenueShare",
                            authority: permission.revenueShare
                        },
                        component: () =>
                            loadComponent(
                                () => import("../reporting/components/RevenueShare.vue"),
                                ["reporting"]
                            )
                    },
                    {
                        name: "agentSource",
                        path: "agentSource",
                        redirect: "/reporting/agentSourceIndex"
                    },
                    {
                        name: "emailAccountability",
                        path: "emailAccountability",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "emailAccountability",
                            authority: [
                                permission.notLender.bind(null, { name: "home" }),
                                permission.reporting
                            ]
                        },
                        component: () =>
                            loadComponent(
                                () => import("../reporting/components/emailAccountability.vue"),
                                "reporting"
                            )
                    },
                    // 1
                    {
                        name: "agentPerformance",
                        path: "agentPerformance",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "agentPerformance",
                            authority: [
                                permission.notLender.bind(null, { name: "home" }),
                                permission.reporting
                            ]
                        },
                        component: () =>
                            loadComponent(
                                () => import("../reporting/components/agentPerformance.vue"),
                                "reporting"
                            )
                    },
                    {
                        name: "trafficReport",
                        path: "trafficReport",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "trafficReport",
                            byPassAuthority: true
                        },
                        component: () =>
                            loadComponent(
                                () => import("../reporting/components/trafficReport.vue"),
                                "reporting"
                            )
                    },
                    {
                        name: "agentSource",
                        path: "agentSource",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "agentSource",
                            byPassAuthority: true
                        },
                        component: () =>
                            loadComponent(
                                () => import("../reporting/components/agentSource.vue"),
                                "reporting"
                            )
                    },
                    {
                        name: "agentRecord",
                        path: "agentRecord",
                        meta: {
                            title: "Reporting | Lofty",
                            headerMenuName: "agentRecord",
                            authority: [permission.agentRecord]
                        },
                        component: () =>
                            loadComponent(
                                () => import("../reporting/components/agentRecord.vue"),
                                "reporting"
                            )
                    }
                ]
            },
        {
            path: "/buyerAlert",
            redirect: "/alert"
        },
        {
            path: "/sellerAlert",
            redirect: "/alert"
        },
        {
            name: "alert",
            path: "/alert",
            meta: {
                title: "Alert | LoftyWorks",
                hideChat: true,
                authority: permission.alert
            },
            component: () => {
                const searchCenter = crmUtils.getParameterByName("searchCenter");
                let useSearchCenter = false;

                if (searchCenter === "" || searchCenter === null) {
                    useSearchCenter = permission.useSearchCenter();
                } else {
                    useSearchCenter = searchCenter === "true";
                }

                return loadComponent(
                    () =>
                        import(
                            /* webpackPrefetch: true */ `../alert${
                                useSearchCenter ? "-v2" : ""
                            }/index.vue`
                        ),
                    ["alert", "searchCondition"]
                );
            }
        },
        {
            name: "buyerTour",
            path: "/propertyTour",
            meta: {
                title: "Property Tour Tool | LoftyWorks",
                hideChat: true
                //  Let go all for now
                // authority: permission.alert
            },
            component: () => {
                return loadComponent(
                    () => import(/* webpackPrefetch: true */ `../buyerTour/index.vue`),
                    ["buyerTour"]
                );
            }
        },
        {
            name: "homeownerMap",
            path: "/homeownerMap",
            meta: {
                title: "Marketing | LoftyWorks",
                hideChat: true
                // homeowner  access control
                // authority: permission.homeowner
            },
            component: () =>
                loadComponent(
                    () => import(/* webpackPrefetch: true */ "../homeownerMap/index.vue"),
                    "searchCondition"
                )
        },
        {
            name: "handpick",
            path: "/handpick",
            meta: {
                title: "Alert | LoftyWorks",
                hideChat: true,
                authority: permission.alert
            },
            component: () =>
                loadComponent(
                    () =>
                        import(
                            /* webpackPrefetch: true */ `../alert${
                                permission.useSearchCenter() ? "-v2" : ""
                            }/handPick.vue`
                        ),
                    "searchCondition"
                )
        },
        {
            name: "letting_accounting",
            path: "/letting-accounting",
            meta: {
                bodyClass: "settings",
                title: "Accounting | LoftyWorks"
            },
            component: () =>
                loadComponent(
                    () =>
                        import(
                            /* webpackPrefetch: true */ "../letting-accounting/index.vue"
                        ),
                    "setting-preference"
                )
        },
        {
            name: "listingmgmt",
            path: "/listingmgmt",
            meta: {
                title: "Property Discovery | LoftyWorks",
                bodyClass: "listingmgmt",
                authority: permission.checkAuth.bind(
                    null,
                    ({ userInfo }) => userInfo.listingMgmtWhiteList && userInfo.hasBuiltSite,
                    { name: "home" }
                ),
                headerMenuName: "listing"
            },
            component: () =>
                loadComponent(ListingManagementComponent, ["listingManagement", "searchCondition"])
        },
        {
            name: "PropertyManagement",
            path: "/property",
            meta: {
                title: "Property Management | LoftyWorks",
                bodyClass: "listingmgmt",
                authority: permission.checkAuth.bind(
                    null,
                    ({ userInfo }) => userInfo.listingMgmtWhiteList && userInfo.hasBuiltSite,
                    { name: "home" }
                ),
                headerMenuName: "listing"
            },
            component: () =>
                loadComponent(() => import("../property-management/index.vue"), ["property"])
        },
        {
            name: "propertyDetail",
            path: "/:type(sales|lettings)/property/detail",
            meta: {
                title: "Property Detail | LoftyWorks",
                bodyClass: "listingmgmt",
                headerMenuName: "listing"
            },
            component: () =>
                loadComponent(() => import("../property-management/detail/index.vue"), ["property"])
        },
        {
            name: "smartListing",
            path: "/smartlisting",
            meta: {
                title: `Smart ${isUK ? "Property" : "Listing"} | LoftyWorks`,
                bodyClass: "smartlisting",
                authority: permission.checkAuth.bind(
                    null,
                    ({ userInfo }) => userInfo.hasBuiltSite,
                    { name: "home" }
                )
            },
            component: () =>
                loadComponent(() => import(`../smart-listing/index.vue`), ["smartListing"])
        },
        {
            name: "AddMlsFeed",
            path: "/add-listing",
            meta: {
                title: "Add Listing | LoftyWorks",
                bodyClass: "edit-mls-feed",
                hideChat: true
            },
            component: () => loadComponent(() => import(`../mlsFeedPage.vue`), [])
        },
        {
            name: "EditMlsFeed",
            path: "/edit-listing",
            meta: {
                title: "Edit Listing | LoftyWorks",
                bodyClass: "edit-mls-feed",
                hideChat: true
            },
            component: () => loadComponent(() => import(`../mlsFeedPage.vue`), [])
        },
        {
            name: "showingConfig",
            path: "/showingConfig",
            meta: {
                title: "Showing Config | LoftyWorks",
                hideChat: true,
                bodyClass: "showingConfig"
            },
            component: () =>
                loadComponent(() => import(`../showing-config/index.vue`), ["showingConfig"])
        },
        {
            name: "transactionmgmt",
            path: "/transaction",
            meta: {
                title: "Transactions | LoftyWorks",
                hideChat: false,
                bodyClass: "transactionmgmt",
                authority: permission.transaction,
                headerMenuName: "transaction"
            },
            component: () =>
                loadComponent(
                    () =>
                        import(
                            /* webpackChunkName: "js/transactionmgmt",webpackPrefetch: true */ "../transaction-management/app.vue"
                        ),
                    "transactionManagement"
                )
        },
        {
            name: "transactionDetail",
            path: "/transaction/detail",
            meta: {
                title: "Transactions | LoftyWorks",
                hideChat: false,
                bodyClass: "transaction-detail",
                authority: permission.transaction
            },
            component: () =>
                loadComponent(
                    () =>
                        import(
                            /* webpackChunkName: "js/transactionDetail" */ "../transaction-detail/App.vue"
                        ),
                    "transactionManagement"
                )
        },
        {
            // setting  old page compatible
            path: "/usersetting",
            redirect: (to) => {
                if (to.query.page) {
                    if (newSettingPages.indexOf(to.query.page) !== -1) {
                        return `/usersetting/${to.query.page}`;
                    } else if (oldSettingPages.indexOf(to.query.page) == -1) {
                        return "/usersetting/profile";
                    } else if (to.query.page === "virtualNumber") {
                        window.location.href =
                            "https://chimecrm.zendesk.com/hc/en-us/sections/115001400463-Dialer-Beta-";
                    }
                }
                return { name: "usersetting" };
            }
        },
        {
            name: "usersetting",
            path: "/usersetting",
            redirect: "/usersetting/profile",
            component: () =>
                loadComponent(
                    () => import(/* webpackPrefetch: true */ "../usersetting/usersetting.vue"),
                    "setting"
                ),
            meta: {
                bodyClass: "settings",
                title: "Settings | LoftyWorks",
                getPageName: (route) => {
                    return `setting_${route.query.page}`;
                },
                authority: permission.setting,
                headerMenuName: "settings"
            },
            children: [
                {
                    name: "setting_mfa",
                    path: "mfa",
                    meta: {
                        bodyClass: "settings",
                        title: "Security | LoftyWorks",
                        authority: (_) => {
                            let userInfo = infoData.getUserInfo();
                            if (userInfo.isBrokerAgent) {
                                return { name: "setting_notification" };
                            }
                        }
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(/* webpackPrefetch: true */ "../usersetting/mfa/index.vue"),
                            ["commonModule", "setting-mfa"]
                        )
                },
                {
                    name: "setting_profile",
                    path: "profile",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: () => {
                            return { name: "setting_notification" };
                        }
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/profile/newIndex.vue"
                                ),
                            "setting-profile"
                        )
                },
                // {
                //     name: "setting_preference",
                //     path: "preference",
                //     meta: {
                //         bodyClass: "settings",
                //         title: "Settings | LoftyWorks"
                //     },
                //     component: () =>
                //         loadComponent(
                //             () =>
                //                 import(
                //                     /* webpackPrefetch: true */ "../usersetting/preference/index.vue"
                //                 ),
                //             "setting-preference"
                //         )
                // },
                {
                    name: "setting_manageDomain",
                    path: "manageDomain",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: permission.checkAuth.bind(
                            null,
                            ({ checkUserRight }) =>
                                checkUserRight("MANAGE_TEAM_AGENT") ||
                                checkUserRight("MANAGE_TEAM_AGENT_DEPARTMENT"),
                            { name: "setting_notification" }
                        )
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/manageDomain/index.vue"
                                ),
                            "setting-preference"
                        )
                },
                {
                    name: "setting_notification",
                    path: "notification",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks"
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/notification/index.vue"
                                ),
                            "setting-notification"
                        )
                },
                {
                    name: "setting_agent",
                    path: "agent",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: permission.setting_agent
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/agent/index.vue"
                                ),
                            ["setting", "setting-agent"]
                        )
                },
                {
                    name: "setting_agent_detail",
                    path: "agentDetail",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: permission.setting_agent
                    },
                    component: () =>
                        loadComponent(
                            () => import("../usersetting/agent/agentList/agentDetail/index.vue"),
                            ["setting", "setting-agent"]
                        )
                },
                {
                    name: "setting_agent_invite_existing_user",
                    path: "inviteExistingUser",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: permission.checkAuth.bind(
                            null,
                            ({ basePermission }) => basePermission.TeamLink,
                            { name: "setting_agent" }
                        )
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    "../usersetting/agent/agentList/inviteExistingUser/index.vue"
                                ),
                            ["setting", "setting-agent"]
                        )
                },
                {
                    name: "setting_companyProfile",
                    path: "companyProfile",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: [permission.company_profile]
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/companyProfile/index.vue"
                                ),
                            "setting-companyProfile"
                        )
                },
                {
                    name: "setting_office",
                    path: "office",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: [permission.setting_office]
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/office/index.vue"
                                ),
                            "setting-office"
                        )
                },
                {
                    name: "setting_lender",
                    path: "lender",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: permission.setting_notLender
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/lender/index.vue"
                                ),
                            "setting-lender"
                        )
                },
                {
                    name: "setting_leadPond",
                    path: "leadPond",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: permission.settingLeadPond
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/leadPond/index.vue"
                                ),
                            "setting-leadPond"
                        )
                },
                {
                    name: "setting_vendor",
                    path: "vendor",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: permission.setting_vendor
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/vendor/index.vue"
                                ),
                            "setting-vendor"
                        )
                },
                {
                    name: "setting_autoAlert",
                    path: "autoAlert",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: permission.setting_autoAlert
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/autoPropertyAlert/index.vue"
                                ),
                            "setting-autoPropertyAlert"
                        )
                },
                {
                    name: "setting_teamRouting",
                    path: "teamRouting",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: permission.setting_teamRouting
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/routing/teamRouting/index.vue"
                                ),
                            "setting-routing"
                        )
                },
                {
                    name: "setting_leadRouting",
                    path: "leadRouting",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: permission.setting_leadRouting
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/routing/leadRouting/index.vue"
                                ),
                            "setting-routing"
                        )
                },
                {
                    name: "setting_assignGroupMgt",
                    path: "assignGroupMgt",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: permission.setting_assignGroupMgt
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/routing/AssignmentGroup/index.vue"
                                ),
                            "setting-routing"
                        )
                },
                {
                    name: "setting_reRoutingGroup",
                    path: "reRoutingGroup",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: permission.lead_rerouting
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/routing/reRoutingGroup/index.vue"
                                ),
                            "setting-routing"
                        )
                },
                {
                    name: "setting_emailTemplate",
                    path: "emailTemplate",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks"
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/emailTemplate/index.vue"
                                ),
                            "setting-emailTemplate"
                        )
                },
                {
                    name: "setting_smartPlan",
                    path: "smartPlan",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: permission.setting_smartplan
                    },
                    beforeEnter(to, from, next) {
                        const { teamType } = infoData.getUserInfo();

                        if (teamType === 2) {
                            next();
                        } else {
                            next({
                                name: "campaigns_smartplans",
                                query: to.query,
                                params: to?.params
                            });
                        }
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/smartPlan/index.vue"
                                ),
                            ["setting", "setting-smartPlan", "campaigns-designCenter"]
                        )
                },
                {
                    name: "setting_customField",
                    path: "customField",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: permission.setting_customField
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/customFields/index.vue"
                                ),
                            "setting-customFields"
                        )
                },
                {
                    name: "setting_AIAssistant",
                    path: "AIAssistant",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: permission.AIAssistant
                    },
                    component: () => {
                        return loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/AIAssistant/index.vue"
                                ),
                            ["setting", "setting-AIAssistant"]
                        );
                    }
                },
                {
                    name: "setting_AIoverview",
                    path: "AIoverview",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks"
                    },
                    component: () => {
                        return loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/AICopilot/overview.vue"
                                ),
                            ["setting", "setting-AIAssistant"]
                        );
                    }
                },
                {
                    name: "setting_AICopilot",
                    path: "AICopilot",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks"
                    },
                    component: () => {
                        return loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/AICopilot/index.vue"
                                ),
                            ["setting", "setting-AIAssistant"]
                        );
                    }
                },
                {
                    name: "setting_AIMarketer",
                    path: "AIMarketer",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks"
                    },
                    component: () => {
                        return loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/AICopilot/aiMarketerIndex.vue"
                                ),
                            ["setting", "setting-AIAssistant"]
                        );
                    }
                },
                {
                    name: "setting_import_jira",
                    path: "importJiraLinkPage",
                    meta: {
                        bodyClass: "settings",
                        hideChat: true,
                        title: "Settings | LoftyWorks",
                        authority: permission.setting_notLender
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/leadImport/modules/RequestOurHelp/jiraLinkPage.vue"
                                ),
                            "setting-leadImport"
                        )
                },
                {
                    name: "setting_import",
                    path: "import",
                    meta: {
                        bodyClass: "settings",
                        hideChat: true,
                        title: "Settings | LoftyWorks",
                        authority: permission.setting_notLender
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/leadImport/index.vue"
                                ),
                            "setting-leadImport"
                        )
                },
                {
                    name: "setting_export",
                    path: "export",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: permission.setting_export
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/leadExport/index.vue"
                                ),
                            "setting-leadExport"
                        )
                },
                {
                    name: "setting_whiteLabel",
                    path: "whiteLabel",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: permission.setting_whiteLabel
                    },
                    component: () =>
                        loadComponent(
                            () => import("../usersetting/whiteLabel/whiteLabel.vue"),
                            "setting-whiteLabel"
                        )
                },
                {
                    name: "setting_sso",
                    path: "sso",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: permission.SSOSetting
                    },
                    component: () =>
                        loadComponent(() => import("../usersetting/sso/index.vue"), "setting-sso")
                },
                //  reconstructed billing page
                {
                    name: "setting_billing",
                    path: "billing",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks"
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/billing/index.vue"
                                ),
                            "setting-billing"
                        )
                },
                {
                    name: "setting_addQuestionnaire",
                    path: "addQuestionnaire/:id",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: permission.setting_call
                    },
                    component: () =>
                        loadComponent(
                            () => import("../usersetting/templateSettings/AddQuestion/index.vue"),
                            ["setting", "setting-templateSettings"]
                        )
                },
                {
                    name: "setting_checklistTemplates",
                    path: "checklistTemplates",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        hideChat: true
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/transaction/index.vue"
                                ),
                            "setting-transaction"
                        )
                },
                {
                    name: "setting_transactionForm",
                    path: "transactionForm",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: permission.setting_transactionCustomField
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/transaction/TransactionForm.vue"
                                ),
                            ["setting-transactionForm", "setting-transaction"]
                        )
                },
                {
                    name: "setting_transactionRoles",
                    path: "transactionRoles",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: [permission.setting_transactionCustomField]
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/transaction/TransactionRoles.vue"
                                ),
                            "setting-transaction"
                        )
                },
                {
                    name: "setting_transactionCommission",
                    path: "transactionCommission",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: [permission.setting_transactionCommission]
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/transaction/TransactionCommission.vue"
                                ),
                            ["setting-transaction", "setting-transactionCommissionSetting"]
                        )
                },
                {
                    name: "setting_transactionFields",
                    path: "transactionFields",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: [permission.setting_transactionCustomField]
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/transaction/transactionFields/index.vue"
                                ),
                            "setting-transaction"
                        )
                },
                {
                    name: "setting_manageDialer",
                    path: "manageDialer",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: permission.dialer
                    },
                    component: () =>
                        loadComponent(
                            () => import("../usersetting/manageDialer/index.vue"),
                            "setting-manageDialer"
                        )
                },
                {
                    name: "setting_appCenter",
                    path: "appCenter",
                    meta: {
                        bodyClass: "settings",
                        title: "Setting | LoftyWorks"
                    },
                    component: () =>
                        loadComponent(
                            () => import("../usersetting/Integration/index.vue"),
                            "setting-Integration"
                        )
                },
                {
                    name: "setting_leadCapture",
                    path: "leadCapture",
                    meta: {
                        bodyClass: "settings",
                        title: "Setting | LoftyWorks",
                        authority: permission.setting_leadCapture
                    },
                    component: () =>
                        loadComponent(
                            () => import("../usersetting/leadCapture/index.vue"),
                            "setting-leadCapture"
                        )
                },

                // tags
                {
                    name: "setting_tags",
                    path: "tags",
                    meta: {
                        bodyClass: "settings",
                        title: "Setting | LoftyWorks",
                        getNewPageName: (_route) => {
                            return `setting_tags`;
                        }
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackChunkName: "js/setting/tags" */ "../usersetting/tags/index.vue"
                                ),
                            "setting-tags"
                        )
                },
                {
                    name: "setting_source",
                    path: "source",
                    meta: {
                        bodyClass: "settings",
                        title: "Setting | LoftyWorks",
                        getNewPageName: (_route) => {
                            return `setting_source`;
                        }
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackChunkName: "js/setting/source" */ "../usersetting/source/index.vue"
                                ),
                            "setting-tags" //语言包
                        )
                },

                // reporting

                {
                    name: "setting_reporting",
                    path: "reporting",
                    meta: {
                        bodyClass: "reporting",
                        title: "Reporting | LoftyWorks",
                        getNewPageName: (_route) => {
                            return `setting_reporting`;
                        },
                        authority: permission.setting_reporting
                    },
                    component: () =>
                        loadComponent(
                            () => import("../usersetting/reporting/index.vue"),
                            "setting-reporting"
                        )
                },

                {
                    name: "setting_agentPartner",
                    path: "agentPartner",
                    meta: {
                        bodyClass: "agentPartner",
                        title: "AgentPartner | LoftyWorks",
                        getNewPageName: (_route) => {
                            return `setting_agentPartner`;
                        },
                        authority: permission.setting_agentPartner
                    },
                    component: () =>
                        loadComponent(
                            import(
                                /* webpackChunkName: "js/setting/agentPartner" */ "../usersetting/agentPartner/index.vue"
                            ),
                            "setting-agentPartner"
                        )
                },
                {
                    name: "setting_transferPipeline",
                    path: "transferPipeline",
                    meta: {
                        bodyClass: "settings",
                        title: "Settings | LoftyWorks",
                        authority: permission.transferPipeline
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/TransferPipeline/index.vue"
                                ),
                            "setting-transferPipeline"
                        )
                },
                {
                    path: "*",
                    redirect: "/usersetting/profile"
                }
            ]
        },
        {
            name: "userbilling",
            path: "/userbilling",
            meta: {
                title: "Upgrade | LoftyWorks",
                hideChat: true,
                bodyClass: "billings",
                authority: permission.checkAuth.bind(
                    null,
                    ({ userInfo, checkUserRight }) =>
                        checkUserRight("newBilling") && userInfo.teamType === 0,
                    { name: "home" }
                )
            },
            component: () => loadComponent(() => import("../userbilling/app.vue"), "user-billing")
        },
        {
            name: "billingupgrade",
            path: "/billingupgrade",
            meta: {
                title: "Upgrade | LoftyWorks",
                hideChat: true,
                bodyClass: "billings",
                authority: permission.notLender.bind(null, { name: "home" })
            },
            component: () =>
                loadComponent(
                    () => import(/* webpackPrefetch: true */ "../userbilling/upgrade.vue"),
                    "user-billing"
                )
        },
        {
            name: "popUp",
            path: "/popUp",
            meta: {
                title: "Upgrade | LoftyWorks",
                hideChat: true,
                bodyClass: "billings"
            },
            component: () =>
                loadComponent(import("../../js/common-module/SuccessPop/simpleSuccess.vue"))
        },
        {
            name: "marketPlace",
            path: "/marketPlace",
            meta: {
                title: "Marketplace | LoftyWorks",
                hideChat: true,
                bodyClass: "marketplace",
                headerMenuName: "marketplace",
                authority: permission.marketplace
            },
            component: () =>
                loadComponent(
                    () => import("../marketplace/index.vue"),
                    ["marketPlace", "setting-revaluate"]
                )
        },
        {
            name: "website",
            path: "/website",
            meta: {
                title: "Landing Page | LoftyWorks",
                hideChat: true,
                bodyClass: "website",
                authority: permission.notLender.bind(null, { name: "home" })
            },
            component: () => loadComponent(() => import("../website/index.vue"), "website")
        },
        {
            name: "pkgUpsell",
            path: "/pkgUpsell",
            meta: {
                title: "Marketplace | LoftyWorks",
                hideChat: true,
                bodyClass: "marketplace"
                // authority: [permission.pkgUpsell, permission.freeTrialExpired]
            },
            component: () =>
                loadComponent(() => import("../marketplace/pkgUpsell/index.vue"), "pkgUpsell")
        },
        {
            name: "buyAdditionalWebsites",
            path: "/buyAdditionalWebsites",
            meta: {
                title: "Marketplace | LoftyWorks",
                hideChat: true,
                bodyClass: "marketplace",
                authority: permission.buyAgentWebsite
            },
            component: () =>
                loadComponent(
                    () => import("../marketplace/buyAgentWebsite/agentWebsite.vue"),
                    "marketPlace"
                )
        },
        {
            name: "buyFullSEOWebsite",
            path: "/buyFullSEOWebsite",
            meta: {
                title: "Marketplace | LoftyWorks",
                hideChat: true,
                bodyClass: "marketplace"
                // authority: permission.buyAgentWebsite
            },
            component: () =>
                loadComponent(
                    () => import("../marketplace/buyAgentWebsite/fullSeo.vue"),
                    "marketPlace"
                )
        },
        {
            name: "buyVanityDomainWebsite",
            path: "/buyVanityDomainWebsite",
            meta: {
                title: "Marketplace | Lofty",
                hideChat: true,
                bodyClass: "marketplace"
            },
            component: () =>
                loadComponent(
                    () => import("../marketplace/buyAgentWebsite/vanityDomain.vue"),
                    "marketPlace"
                )
        },
        {
            name: "buyBrandBundle",
            path: "/buyBrandBundle",
            meta: {
                title: "Marketplace | LoftyWorks",
                hideChat: true,
                bodyClass: "marketplace",
                authority: permission.buyBrandBundle
            },
            component: () =>
                loadComponent(
                    () => import("../marketplace/buyBrandBundle/index.vue"),
                    "marketPlace"
                )
        },

        // {
        //     name: "buyAgentCorporate",
        //     path: "/buyAgentCorporate",
        //     meta: {
        //         title: "Marketplace | LoftyWorks",
        //         hideChat: true,
        //         bodyClass: "marketplace",
        //         authority: permission.buyAgentCorporate
        //     },
        //     component: () =>
        //         loadComponent(
        //             () => import("../marketplace/buyAgentWebsite/corporate.vue"),
        //             "marketPlace"
        //         )
        // },
        {
            name: "buyMultilingual",
            path: "/buyMultilingual",
            meta: {
                title: "Marketplace | LoftyWorks",
                hideChat: true,
                bodyClass: "marketplace",
                authority: permission.notLender.bind(null, { name: "home" })
            },
            component: () =>
                loadComponent(
                    () => import("../marketplace/buyMultilingual/index.vue"),
                    "marketPlace"
                )
        },
        {
            name: "buyChimeCMA",
            path: "/buyChimeCMA",
            meta: {
                title: "Marketplace | LoftyWorks",
                hideChat: true,
                bodyClass: "marketplace",
                authority: permission.notLender.bind(null, { name: "home" })
            },
            component: () =>
                loadComponent(() => import("../marketplace/buyChimeCMA/index.vue"), "marketPlace")
        },
        {
            name: "buyHomeMaxi",
            path: "/buyHomeMaxi",
            meta: {
                title: "Marketplace | LoftyWorks",
                hideChat: true,
                headerMenuName: "marketplace",
                authority: permission.notLender.bind(null, { name: "home" })
            },
            component: () =>
                loadComponent(() => import("../marketplace/buyHomeMaxi/index.vue"), "marketPlace")
        },
        {
            name: "buySocialMedia",
            path: "/buySocialMedia",
            meta: {
                title: "Marketplace | LoftyWorks",
                hideChat: true,
                bodyClass: "marketplace",
                authority: permission.notLender.bind(null, { name: "home" })
            },
            component: () =>
                loadComponent(
                    () =>
                        import(
                            /* webpackChunkName: "js/buy-social-media" */ "../marketplace/buySocialMedia/index.vue"
                        ),
                    "marketPlace"
                )
        },
        {
            name: "buyAiPackage",
            path: "/buyAiPackage",
            meta: {
                title: "Marketplace | LoftyWorks",
                hideChat: true,
                bodyClass: "marketplace",
                authority: permission.notLender.bind(null, { name: "home" })
            },
            component: () =>
                loadComponent(() => import("../marketplace/buyAiPackage/index.vue"), "marketPlace")
        },
        {
            name: "buyWordPressPlugin",
            path: "/buyWordPressPlugin",
            meta: {
                title: "Marketplace | LoftyWorks",
                hideChat: true,
                bodyClass: "marketplace",
                authority: permission.notLender.bind(null, { name: "home" })
            },
            component: () =>
                loadComponent(
                    () => import("../marketplace/buyWordPressPlugin/index.vue"),
                    "marketPlace"
                )
        },
        {
            name: "buyDomain",
            path: "/buyDomain",
            meta: {
                title: "Marketplace | LoftyWorks",
                hideChat: true,
                bodyClass: "marketplace",
                authority: permission.notLender.bind(null, { name: "home" })
            },
            component: () =>
                loadComponent(() => import("../marketplace/buyDomain/index.vue"), "marketPlace")
        },
        {
            name: "buyDepartmentSite",
            path: "/buyDepartmentSite",
            meta: {
                title: "Marketplace | LoftyWorks",
                hideChat: true,
                bodyClass: "marketplace",
                authority: permission.notLender.bind(null, { name: "home" })
            },
            component: () =>
                loadComponent(
                    () => import("../marketplace/buyDepartmentSite/index.vue"),
                    "marketPlace"
                )
        },
        {
            name: "buyListingPromotion",
            path: "/buyListingPromotion",
            meta: {
                title: "Marketing | LoftyWorks",
                hideChat: true,
                bodyClass: "campaigns",
                authority: permission.listingAds
            },
            component: () =>
                loadComponent(
                    () => import("../campaigns/listingAds/buyListingPromotion/index.vue"),
                    "campaigns-listingAds"
                )
        },
        {
            name: "buyListingAdsEnterprise",
            path: "/buyListingAdsEnterprise",
            meta: {
                hideChat: true,
                bodyClass: "campaigns"
            },
            component: () =>
                loadComponent(
                    () => import("../campaigns/listingAds/buyListingAdsEnterprise/index.vue"),
                    "campaigns-listingAds"
                )
        },
        {
            name: "printCenterUpdatePlan",
            path: "/printCenterUpdatePlan",
            meta: {
                title: "Marketing | LoftyWorks",
                hideChat: true,
                bodyClass: "campaigns"
            },
            component: () =>
                loadComponent(
                    () => import("../campaigns/printCenter/updatePlan/index.vue"),
                    ["campaignOnboarding", "printCenter", "campaigns-designCenter"]
                )
        },
        //  batch add member
        {
            name: "batchAddMember",
            path: "/batchAddMember",
            meta: {
                bodyClass: "settings",
                title: "Settings | LoftyWorks",
                hideChat: true,
                authority: permission.batchAddAgent
            },
            component: () =>
                loadComponent(
                    loadComponent(
                        () => import("../usersetting/agent/agentList/batchAddMember/index.vue"),
                        ["setting", "setting-agent"]
                    )
                )
        },
        {
            name: "notfound",
            path: "/notfound",
            meta: {
                title: "LoftyWorks",
                hideChat: true
            },
            component: () =>
                loadComponent(() => import("../error/unavailable-lead/index.vue"), ["notfound"])
        },
        /**
         *  Christmas sale purchase page   address ：https://127.0.0.1:8081/admin/home/<USER>
         *  This page is not a self-service purchase page
         */
        {
            name: "campaignsBuyerLeadGenForAds",
            path: "/:type(buyerLeadGen|sellerLeadGen)",
            meta: {
                // bodyClass: "campaigns",
                title: (route) =>
                    `${
                        route.params.type.toLowerCase() === "sellerleadgen" ? "Seller" : "Buyer"
                    } Lead Gen | LoftyWorks`,
                hideChat: true,
                hideSideMenu: true
            },
            component: () =>
                loadComponent(
                    () =>
                        import(
                            /* webpackChunkName: "js/leadEngine/leadGenForAds" */ "../campaigns/leadEngine/leadGenForAds"
                        ),
                    "campaigns-leadEngine"
                ),
            props: (route) => {
                return {
                    type: route.params.type
                };
            }
        },
        {
            name: "campaigns",
            path: "/campaigns",
            component: () =>
                loadComponent(() => import("../campaigns/app.vue"), "campaigns-dashboard"),
            meta: {
                title: "Marketing | LoftyWorks",
                bodyClass: "campaigns",
                authority: [permission.notLender.bind(null, { name: "home" }), permission.campaign],
                headerMenuName: "campaign"
            },
            children: [
                {
                    name: "campaigns_dashboard",
                    path: "dashboard",
                    meta: {
                        bodyClass: "campaigns",
                        title: "Marketing | LoftyWorks",
                        hideChat: false
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackChunkName: "js/campaigns/dashboard",webpackPrefetch: true */ "../campaigns/Dashboard"
                                ),
                            "campaigns-dashboard"
                        )
                },
                {
                    name: "campaigns_buyerLeadGen",
                    path: "buyerLeadGen",
                    meta: {
                        bodyClass: "campaigns",
                        title: "Marketing | LoftyWorks",
                        // 317 frontend blacklist
                        authority: permission.checkAuth.bind(
                            null,
                            ({ userInfo, basePermission }) =>
                                (!blackList.leadGenBlackList.includes(userInfo.teamId) ||
                                    userInfo.isTeamOwner) &&
                                basePermission.ListingAds,
                            { name: "home" }
                        )
                    },
                    component: () =>
                        loadComponent(
                            () => import("../campaigns/leadEngine/index"),
                            ["marketPlace", "campaigns-leadEngine"]
                        )
                },
                {
                    name: "campaigns_sellerLeadGen",
                    path: "sellerLeadGen",
                    meta: {
                        bodyClass: "campaigns",
                        title: "Marketing | LoftyWorks",
                        // 317 frontend blacklist
                        authority: permission.checkAuth.bind(
                            null,
                            ({ userInfo, basePermission }) =>
                                (!blackList.leadGenBlackList.includes(userInfo.teamId) ||
                                    userInfo.isTeamOwner) &&
                                basePermission.ListingAds,
                            { name: "home" }
                        )
                    },
                    component: () =>
                        loadComponent(
                            () => import("../campaigns/leadEngine/index"),
                            ["marketPlace", "campaigns-leadEngine"]
                        )
                },
                {
                    name: "campaigns_remarketingAds",
                    path: "remarketingAds",
                    meta: {
                        bodyClass: "campaigns",
                        title: "Marketing | LoftyWorks",
                        authority: permission.checkAuth.bind(
                            null,
                            ({ userInfo, basePermission }) =>
                                (!blackList.leadGenBlackList.includes(userInfo.teamId) ||
                                    userInfo.isTeamOwner) &&
                                basePermission.ListingAds,
                            { name: "home" }
                        )
                    },
                    component: () =>
                        loadComponent(
                            () => import("../campaigns/remarketingAds/index"),
                            ["marketPlace", "campaigns-leadEngine", "campaigns-remarketingAds"]
                        )
                },
                {
                    name: "campaigns_localServiceAds",
                    path: "localServiceAds",
                    meta: {
                        bodyClass: "campaigns",
                        title: "Marketing | LoftyWorks",
                        hideChat: true,
                        authority: permission.checkAuth.bind(
                            null,
                            ({ basePermission }) => basePermission.ListingAds,
                            { name: "home" }
                        )
                    },
                    component: () =>
                        loadComponent(
                            () => import("../campaigns/localServiceAds/index"),
                            ["campaigns-localServiceAds", "campaigns-leadEngine"]
                        )
                },
                {
                    name: "campaigns_sphereAds",
                    path: "sphereAds",
                    meta: {
                        bodyClass: "campaigns",
                        title: "Marketing | LoftyWorks",
                        hideChat: true,
                        authority: permission.checkAuth.bind(
                            null,
                            ({ basePermission }) => basePermission.ListingAds,
                            { name: "home" }
                        )
                    },
                    component: () =>
                        loadComponent(
                            () => import("../campaigns/sphereAds/index"),
                            ["campaigns-sphere", "campaigns-leadEngine", "marketPlace"]
                        )
                },
                {
                    name: "campaigns_buySphereAds",
                    path: "buySphereAds",
                    meta: {
                        bodyClass: "campaigns",
                        title: "Marketing | LoftyWorks",
                        hideChat: true,
                        authority: permission.checkAuth.bind(
                            null,
                            ({ basePermission }) => basePermission.ListingAds,
                            { name: "home" }
                        )
                    },
                    component: () =>
                        loadComponent(
                            () => import("../campaigns/sphereAds/module/purchase/index"),
                            ["campaigns-sphere", "campaigns-leadEngine"]
                        )
                },
                {
                    name: "campaigns_sphereAds_detail",
                    path: "sphereAds/detail",
                    meta: {
                        bodyClass: "campaigns",
                        title: "Marketing | LoftyWorks",
                        hideChat: true
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    "../campaigns/sphereAds/module/dashboard/module/detail/index.vue"
                                ),
                            ["campaigns-sphere", "campaigns-leadEngine"]
                        )
                },
                {
                    name: "campaigns_sphereAds_edit",
                    path: "sphereAds/edit",
                    meta: {
                        bodyClass: "campaigns",
                        title: "Marketing | LoftyWorks",
                        hideChat: true
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    "../campaigns/sphereAds/module/dashboard/module/edit/index.vue"
                                ),
                            ["campaigns-sphere", "campaigns-leadEngine"]
                        )
                },
                /**
                 * leadgen Self-service purchase page
                 */
                {
                    name: "campaigns_buyLeadGen",
                    path: "buyLeadGen",
                    meta: {
                        bodyClass: "campaigns",
                        title: "Marketing | LoftyWorks",
                        hideChat: true,
                        // 317 frontend blacklist
                        // authority: permission.campaigns_buyLeadGen,
                        hideSideMenu: true
                    },
                    component: () =>
                        loadComponent(
                            () => import("../campaigns/leadEngine/buyLeadgen/index.vue"),
                            ["marketPlace", "campaigns-leadEngine"]
                        )
                },
                {
                    name: "campaigns_ListingAds",
                    path: "listingAds",
                    meta: {
                        bodyClass: "campaigns",
                        title: "Marketing | LoftyWorks",
                        authority: permission.listingAds,
                        getPageName: () => "campaigns_ListingAds"
                    },
                    component: () =>
                        loadComponent(
                            () => import("../campaigns/listingAds"),
                            "campaigns-listingAds"
                        )
                },
                {
                    name: "campaigns_zipCodeBlast",
                    path: "zipCodeBlast",
                    meta: {
                        bodyClass: "campaigns",
                        title: "Marketing | LoftyWorks",
                        hideChat: true,
                        getPageName: () => "campaigns_zipCodeBlast"
                    },
                    component: () =>
                        loadComponent(
                            () => import("../campaigns/zipCodeBlast"),
                            "campaigns-zipCodeBlast"
                        )
                },
                {
                    name: "campaigns_designCenter",
                    path: "designCenter",
                    meta: {
                        bodyClass: "campaigns",
                        hideChat: true,
                        title: "Marketing | LoftyWorks",
                        authority: permission.campaign_designCenter
                    },
                    component: () =>
                        loadComponent(
                            () => import("../campaigns/designCenter"),
                            ["campaigns-designCenter", "printCenter", "campaigns-designCenter"]
                        )
                },
                // {
                //     name: "campaigns_postcards",
                //     path: "postcards",
                //     meta: {
                //         bodyClass: "campaigns",
                //         title: "Campaigns | LoftyWorks",
                //         authority: permission.postcards
                //     },
                //     component: () =>
                //         loadComponent(import("../campaigns/postcards"), "geoFarming")
                // },
                {
                    name: "campaigns_homeownerShop",
                    path: "homeownerShop",
                    meta: {
                        bodyClass: "campaigns_homeownerShop",
                        title: "Marketing | LoftyWorks",
                        hideChat: true
                    },
                    component: () =>
                        loadComponent(
                            () => import("../campaigns/homeownerShop/index.vue"),
                            ["campaigns-homeownerShop", "geoFarming"]
                        )
                },
                {
                    name: "campaigns_smartFarm",
                    path: "smartFarm",
                    meta: {
                        bodyClass: "campaigns_homeownerShop",
                        title: "Marketing | LoftyWorks",
                        hideChat: true
                    },
                    component: () =>
                        loadComponent(
                            () => import("../campaigns/geoFarmingV2/index.vue"),
                            ["campaigns-homeownerShop", "geoFarming", "campaigns-leadEngine"]
                        )
                },
                {
                    name: "campaigns_smartFarm_homeowners",
                    path: "smartFarmHomeowners",
                    meta: {
                        bodyClass: "campaigns_homeownerShop",
                        title: "Marketing | LoftyWorks",
                        hideChat: true
                    },
                    component: () =>
                        loadComponent(
                            () => import("../campaigns/geoFarmingV2/homeownerShopPage/index.vue"),
                            ["campaigns-homeownerShop", "geoFarming", "campaigns-leadEngine"]
                        )
                },
                {
                    name: "campaigns_purchaseSmartFarm",
                    path: "purchaseSmartFarm",
                    meta: {
                        bodyClass: "campaigns_purchaseSmartFarm",
                        title: "Marketing | LoftyWorks",
                        hideChat: true,
                        authority: permission.smartFarm
                    },
                    component: () =>
                        loadComponent(
                            () => import("../campaigns/geoFarmingV2/purchaseSmartFarm/index.vue"),
                            ["campaigns-homeownerShop", "geoFarming"]
                        )
                },
                {
                    name: "campaigns_settingSmartFarm",
                    path: "settingSmartFarm",
                    meta: {
                        bodyClass: "campaigns_settingSmartFarm",
                        title: "Marketing | LoftyWorks",
                        hideChat: true
                    },
                    component: () =>
                        loadComponent(
                            () => import("../campaigns/geoFarmingV2/settingSmartFarm/index.vue"),
                            ["campaigns-homeownerShop", "geoFarming"]
                        )
                },
                !isUK && {
                    name: "campaigns_printCenter",
                    path: "printCenter",
                    meta: {
                        bodyClass: "printCenter",
                        hideChat: true
                    },
                    component: () =>
                        loadComponent(
                            () => import("../campaigns/printCenter/index.vue"),
                            ["printCenter", "geoFarming", "campaigns-designCenter"]
                        )
                },
                {
                    name: "campaigns_personalInfo",
                    path: "personalInfo",
                    meta: {
                        bodyClass: "printCenter",
                        hideChat: true
                    },
                    component: () =>
                        loadComponent(
                            () => import("../campaigns/printCenter/personalInfo.vue"),
                            "printCenter"
                        )
                },
                {
                    name: "campaigns_openHouse",
                    path: "openHouse",
                    meta: {
                        bodyClass: "campaigns",
                        title: "Marketing | LoftyWorks"
                    },
                    component: () =>
                        loadComponent(() => import("../campaigns/OpenHouse"), "campaigns-OpenHouse")
                },
                {
                    name: "campaigns_textCode",
                    path: "textCode",
                    meta: {
                        bodyClass: "campaigns",
                        title: "Marketing | LoftyWorks",
                        authority: permission.textCode
                    },
                    component: () =>
                        loadComponent(() => import("../campaigns/textCode"), "campaigns-textCode")
                },
                {
                    name: "campaigns_landingPage",
                    path: "landingPage",
                    meta: {
                        bodyClass: "campaigns",
                        title: "Marketing | LoftyWorks",
                        authority: permission.landingPage
                    },
                    component: () =>
                        loadComponent(
                            () => import("../campaigns/LandingPage"),
                            "campaigns-LandingPage"
                        )
                },
                {
                    name: "campaigns_socialMedia",
                    path: "socialMedia",
                    meta: {
                        bodyClass: "campaigns",
                        title: "Marketing | LoftyWorks",
                        authority: permission.socialMedia
                    },
                    redirect: { name: "socialMedia_dashboard" },
                    component: () =>
                        loadComponent(
                            () => import("../campaigns/SocialMedia"),
                            "campaigns-socialStudio"
                        ),
                    children: [
                        {
                            name: "socialMedia_dashboard",
                            path: "dashboard",
                            meta: {
                                parentName: "campaigns_socialMedia"
                            },
                            component: () =>
                                loadComponent(
                                    import("../campaigns/SocialMedia/dashboard/index.vue")
                                )
                        },
                        {
                            name: "socialMedia_editPost",
                            path: "editPost",
                            meta: {
                                parentName: "campaigns_socialMedia",
                                hideSideMenu: true
                            },
                            component: () =>
                                loadComponent(import("../campaigns/SocialMedia/editPost/index.vue"))
                        },
                        {
                            name: "socialMedia_connect",
                            path: "connect",
                            meta: {
                                parentName: "campaigns_socialMedia"
                            },
                            component: () =>
                                loadComponent(import("../campaigns/SocialMedia/connect/index.vue"))
                        },
                        {
                            name: "socialMedia_autoPost",
                            path: "autoPost",
                            meta: {
                                parentName: "campaigns_socialMedia",
                                hideChat: true
                            },
                            component: () =>
                                loadComponent(import("../campaigns/SocialMedia/autoPost/index.vue"))
                        },
                        {
                            name: "socialMedia_detailPost",
                            path: "detailPost",
                            meta: {
                                parentName: "campaigns_socialMedia"
                            },
                            component: () =>
                                loadComponent(
                                    import(
                                        /* webpackChunkName: "js/campaigns/socialMedia" */ "../campaigns/SocialMedia/detailPost"
                                    )
                                )
                        },
                        {
                            name: "socialMedia_boostPost",
                            path: "boostPost",
                            meta: {
                                parentName: "campaigns_socialMedia",
                                hideSideMenu: true
                            },
                            component: () =>
                                loadComponent(
                                    import(
                                        /* webpackChunkName: "js/campaigns/socialMedia" */ "../campaigns/SocialMedia/boostPost"
                                    )
                                )
                        }
                    ]
                },
                {
                    name: "campaigns_smartplans",
                    path: "smartPlan",
                    meta: {
                        bodyClass: "campaigns",
                        title: "Marketing | LoftyWorks",
                        authority: permission.setting_smartplan
                    },
                    component: () =>
                        loadComponent(
                            () => import("../usersetting/smartPlan/index.vue"),
                            ["setting", "setting-smartPlan", "campaigns-designCenter"]
                        )
                },
                {
                    name: "campaigns_aiAssitant",
                    path: "AIAssistant",
                    meta: {
                        bodyClass: "campaigns",
                        title: "Marketing | LoftyWorks",
                        authority: permission.AIAssistant
                    },
                    component: () =>
                        loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ `../usersetting/AIAssistant/index.vue`
                                ),
                            ["setting", "setting-AIAssistant"]
                        )
                },
                {
                    name: "campaigns_AIoverview",
                    path: "AIoverview",
                    meta: {
                        bodyClass: "campaigns",
                        title: "Marketing | LoftyWorks"
                    },
                    component: () => {
                        return loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/AICopilot/overview.vue"
                                ),
                            ["setting", "setting-AIAssistant"]
                        );
                    }
                },
                {
                    name: "campaigns_AICopilot",
                    path: "AICopilot",
                    meta: {
                        bodyClass: "campaigns",
                        title: "Marketing | LoftyWorks"
                    },
                    component: () => {
                        return loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/AICopilot/index.vue"
                                ),
                            ["setting", "setting-AIAssistant"]
                        );
                    }
                },
                {
                    name: "campaigns_AIMarketer",
                    path: "AIMarketer",
                    meta: {
                        bodyClass: "campaigns",
                        title: "Marketing | LoftyWorks"
                    },
                    component: () => {
                        return loadComponent(
                            () =>
                                import(
                                    /* webpackPrefetch: true */ "../usersetting/AICopilot/aiMarketerIndex.vue"
                                ),
                            ["setting", "setting-AIAssistant"]
                        );
                    }
                },
                {
                    name: "campaigns_autoPropertyAlert",
                    path: "autoAlert",
                    meta: {
                        bodyClass: "campaigns",
                        title: "Marketing | LoftyWorks",
                        authority: permission.campaigns_notLender
                    },
                    component: () =>
                        loadComponent(
                            () => import("../usersetting/autoPropertyAlert/index.vue"),
                            "setting-autoPropertyAlert"
                        )
                },
                // {
                //     name: "campaigns_homeReport",
                //     path: "homeReport",
                //     meta: {
                //         bodyClass: "campaigns",
                //         title: "Marketing | LoftyWorks",
                //         authority: permission.campaign_homereport
                //     },
                //     component: () =>
                //         loadComponent(
                //             () => import("../campaigns/homeReport"),
                //             "campaigns-homeReport"
                //         )
                // },
                // {
                //     name: "campaigns_cma",
                //     path: "cma",
                //     meta: {
                //         bodyClass: "campaigns",
                //         title: "Marketing | LoftyWorks",
                //         authority: permission.cma,
                //         headerMenuName: "campaign"
                //     },
                //     component: () =>
                //         loadComponent(
                //             () => import("../cmas/campaigns-cma/index.vue"),
                //             "campaigns-cma"
                //         )
                // },
                {
                    path: "*",
                    redirect: "/campaigns/dashboard"
                }
            ].filter(Boolean)
        },
        {
            name: "geoFarming_purchaseSmartFarm",
            path: "/geoFarming/purchaseSmartFarm",
            meta: {
                bodyClass: "geoFarming_purchaseSmartFarm",
                title: "Marketing | LoftyWorks",
                hideChat: true,
                authority: permission.smartFarm
            },
            component: () =>
                loadComponent(
                    () => import("../campaigns/geoFarmingV2/purchaseSmartFarm/index.vue"),
                    ["campaigns-homeownerShop", "geoFarming"]
                )
        },
        {
            name: "cma-guide",
            path: "/cma/guide/:step(step1|step2|step3|step4|step5)",
            meta: {
                title: "Build a CMA",
                getPageName: (route) => route.path,
                hideChat: true,
                headerMenuName: "campaign"
            },
            component: () =>
                loadComponent(
                    () => import(/* webpackPrefetch: true */ "../cmas/create-cma/index.vue"),
                    ["searchCondition", "campaigns-cma"]
                ),
            props: (route) => {
                return {
                    step: route.params.step,
                    id: route.query.id,
                    leadId: route.query.leadId
                };
            }
        },
        {
            name: "cma-template",
            path: "/editTemplate",
            meta: {
                title: "CMA Template",
                getPageName: (route) => route.path,
                hideChat: true,
                headerMenuName: "campaign"
            },
            component: () =>
                loadComponent(
                    () =>
                        import(
                            /* webpackPrefetch: true */ "../cmas/template-cma/template-edit.vue"
                        ),
                    ["campaigns-cma"]
                )
        },
        {
            name: "freeTrialExpired",
            path: "/freetrial-expired",
            meta: {
                title: "freeTrialExpired",
                hideChat: true,
                noHeader: false,
                authority: permission.freeTrialExpired
            },
            component: () => loadComponent(import("../freeTrial/freeTrialExpiredPage.vue"))
        },
        {
            name: "buyObServices",
            path: "/buyObServices",
            meta: {
                title: "Marketplace | LoftyWorks",
                hideChat: true,
                headerMenuName: "marketplace",
                // TODO  permissions
                authority: permission.notLender.bind(null, { name: "home" })
            },
            component: () =>
                loadComponent(() => import("../marketplace/buyObServices/index.vue"), "marketPlace")
        },
        {
            name: "buyOfficeAddon",
            path: "/buyOfficeAddon",
            meta: {
                title: "Marketplace | LoftyWorks",
                headerMenuName: "marketplace",
                hideChat: true,
                // TODO  permissions
                authority: permission.notLender.bind(null, { name: "home" })
            },
            component: () =>
                loadComponent(
                    () => import("../marketplace/buyOfficeAddon/index.vue"),
                    "marketPlace"
                )
        },
        {
            name: "buyLoftyBloom",
            path: "/buyLoftyBloom",
            meta: {
                title: "Marketplace | LoftyWorks",
                headerMenuName: "marketplace",
                hideChat: true,
                // TODO  permissions
                authority: permission.notLender.bind(null, { name: "home" })
            },
            component: () =>
                loadComponent(
                    () => import("../marketplace/buyLoftyBloom/index.vue"),
                    ["marketPlace", "campaigns-leadEngine"]
                )
        },
        {
            name: "switchPayerLandingPage",
            path: "/switchPayerLandingPage",
            meta: {
                title: "Marketplace | LoftyWorks",
                headerMenuName: "marketplace",
                hideChat: true
            },
            component: () =>
                loadComponent(
                    () => import("../marketplace/switchPayerLandingPage/index.vue"),
                    "marketPlace"
                )
        },
        {
            name: "officeAddonContractReview",
            path: "/officeAddonContractReview",
            meta: {
                title: "Marketplace | LoftyWorks",
                headerMenuName: "marketplace",
                hideChat: true,
                authority: permission.officeAddonContract
            },
            component: () =>
                loadComponent(
                    () => import("../marketplace/officeAddonContractReview/index.vue"),
                    "marketPlace"
                )
        },
        {
            name: "bloomContractReview",
            path: "/bloomContractReview",
            meta: {
                title: "Marketplace | LoftyWorks",
                headerMenuName: "marketplace",
                hideChat: true
                // authority: permission.bloomContractReview
            },
            component: () =>
                loadComponent(
                    () => import("../marketplace/bloomContractReview/index.vue"),
                    "marketPlace"
                )
        },
        {
            name: "leadReferralLanding",
            path: "/leadReferralLanding",
            meta: {
                bodyClass: "leadReferral",
                title: "Leads | LoftyWorks",
                headerMenuName: "people",
                hideChat: true
            },
            component: () =>
                loadComponent(
                    () =>
                        import(
                            /* webpackChunkName: "js/leadReferralLanding" */ "../leadReferral/index.vue"
                        ),
                    ["leadDetail"]
                )
        },
        {
            name: "debugPage",
            path: "/moatableDebugPage",
            meta: {
                title: "SolveProblems | LoftyWorks",
                hideChat: true,
                headerMenuName: "debugPage"
            },
            component: () =>
                loadComponent(() => import("../moatableDebug/index.vue"), "marketPlace")
        },
        {
            path: "/buySphereAds",
            redirect: "/campaigns/buySphereAds"
        },
        {
            name: "buySphereAdsForFestival",
            path: "/buySphereAdsForFestival",
            meta: {
                bodyClass: "campaigns",
                title: "Marketing | LoftyWorks",
                hideChat: true,
                authority: permission.buySphereAdsForFestival
            },
            component: () =>
                loadComponent(
                    () => import("../campaigns/sphereAds/module/purchase/index"),
                    ["campaigns-sphere", "campaigns-leadEngine"]
                )
        },
        {
            name: "manageAnnouncement",
            path: "/manageAnnouncement",
            meta: {
                bodyClass: "campaigns",
                title: "Marketing | LoftyWorks",
                authority: permission.manageAnnouncement
            },
            component: () =>
                loadComponent(() => import("../chat-window/manageAnnouncement/index"), "chatWindow")
        },
        // {
        //     name: "buySphereAds",
        //     path: "/buySphereAds",
        //     meta: {
        //         bodyClass: "campaigns",
        //         title: "Marketing | LoftyWorks",
        //         hideChat: true,
        //         authority: permission.checkAuth.bind(
        //             null,
        //             ({ basePermission }) =>
        //                 basePermission.ListingAds,
        //             { name: "home" }
        //         )
        //     },
        //     component: () =>
        //         loadComponent(
        //             () => import("../campaigns/sphereAds/module/purchase/index"),
        //             [
        //                 "campaigns-sphere",
        //                 "campaigns-leadEngine"
        //             ]
        //         )
        // },
        {
            name: "closelyWhiteLabelIntroduction",
            path: "/closelyWhiteLabelIntroduction",
            meta: {
                meta: "closely-white-label-introduction",
                title: "Closely White Label Introduction"
            },
            component: () =>
                loadComponent(
                    () => import("../marketplace/whiteLeabelIntroduction/page.vue"),
                    "marketPlace"
                )
        },
        {
            name: "dataMigration",
            path: "/dataMigration",
            meta: {
                bodyClass: "dataMigration",
                title: " LoftyWorks"
            },
            component: () =>
                loadComponent(() => import("../dataMigration/index.vue"), ["dataMigration"])
        },
        {
            name: "key-management",
            path: "/:type(sales|lettings)/key-management",
            meta: {
                title: "Key Management",
                hideChat: true,
                headerMenuName: (route) => route.params.type
            },
            component: () =>
                loadComponent(() => import("../key-managemant/index.vue"), ["keyManagement"])
        },
        {
            path: "*",
            redirect: (_to) => {
                try {
                    const _err = new Error("crm wrong path");
                    _err.name = "crmRouterWrongPath";
                    window.rendebug.sendError(_err, {
                        componentName: "DialerRoot",
                        propsData: null,
                        info: {}
                    });
                    const _hostname = window.location.hostname;
                    if (!["127.0.0.1", "localhost"].includes(_hostname)) {
                        const _str = typeof _to === "string" ? _to : _to?.path;
                        const _pathname = window.location.pathname;
                        new Image().src = `http://alert.d.chime.me/api/notify/re_5c66cd174eaa4c7a86975f72355490b8/routing_bug__${_str.replaceAll(
                            "/",
                            "_"
                        )}_origin_${_pathname.replaceAll("/", "_")}`;
                    }
                } catch (error) {
                    console.log("404 error", error);
                }

                window.location.href = "/admin/404";
            }
        }
    ];
}
