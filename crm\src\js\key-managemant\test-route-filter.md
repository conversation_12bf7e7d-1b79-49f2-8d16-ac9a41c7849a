# Key Management Route Filter Test

## 功能说明
根据路由参数中的type，自动设置Property Type筛选项的值。

## 路由映射
- `/sales/key-management` → Property Type 自动设置为 "For Sale" (for_sale)
- `/lettings/key-management` → Property Type 自动设置为 "To Let" (to_let)

## 实现细节

### SearchFilter.vue 修改内容：

1. **添加路由监听器**
   ```javascript
   watch: {
       '$route'() {
           this.setPropertyTypeFromRoute();
       }
   }
   ```

2. **新增方法 setPropertyTypeFromRoute()**
   - 读取 `this.$route.params.type` 参数
   - 根据参数值设置对应的 propertyTypeFilter
   - sales → for_sale
   - lettings → to_let

3. **新增方法 emitFilterChange()**
   - 统一的筛选器变更事件发送方法
   - 避免代码重复

4. **修改 mounted() 生命周期**
   - 在组件挂载时立即调用 `setPropertyTypeFromRoute()`
   - 确保初始状态正确

## 测试步骤

1. 访问 `/sales/key-management`
   - 验证 Property Type 筛选器显示 "For Sale"
   - 验证筛选器事件正确触发

2. 访问 `/lettings/key-management`
   - 验证 Property Type 筛选器显示 "To Let"
   - 验证筛选器事件正确触发

3. 在页面间切换
   - 验证路由变化时筛选器自动更新

## 注意事项

- 如果路由参数不是 'sales' 或 'lettings'，筛选器保持默认值 'All'
- 筛选器变更会自动触发 filter-change 事件，通知父组件重新加载数据
- 用户仍可手动更改 Property Type 筛选器，不受路由限制
