<template>
    <div class="search-filter">
        <div class="filter-left">
            <div class="search-input-wrapper">
                <i class="icon2017 icon-search_01"></i>
                <input
                    type="text"
                    class="search-input"
                    :placeholder="searchPlaceholder"
                    v-model="searchKeyword"
                    @input="onSearchChange"
                />
            </div>
        </div>
        <div class="filter-right">
            <div class="filter-item">
                <div class="filter-dropdown" @click="toggleDropdown('status')">
                    <span class="filter-label">Status:</span>
                    <span class="filter-value">{{ statusFilter.label }}</span>
                    <i class="icon2017 icon-arrow_01_down"></i>
                </div>
                <div v-if="dropdowns.status" class="dropdown-menu">
                    <div
                        v-for="option in statusOptions"
                        :key="option.value"
                        class="dropdown-item"
                        @click="selectFilter('status', option)"
                    >
                        {{ option.label }}
                    </div>
                </div>
            </div>
            
            <div class="filter-item">
                <div class="filter-dropdown" @click="toggleDropdown('holder')">
                    <span class="filter-label">Holder:</span>
                    <span class="filter-value">{{ holderFilter.label }}</span>
                    <i class="icon2017 icon-arrow_01_down"></i>
                </div>
                <div v-if="dropdowns.holder" class="dropdown-menu">
                    <div
                        v-for="option in holderOptions"
                        :key="option.value"
                        class="dropdown-item"
                        @click="selectFilter('holder', option)"
                    >
                        {{ option.label }}
                    </div>
                </div>
            </div>
            
            <div class="filter-item">
                <div class="filter-dropdown" @click="toggleDropdown('propertyType')">
                    <span class="filter-label">Property Type:</span>
                    <span class="filter-value">{{ propertyTypeFilter.label }}</span>
                    <i class="icon2017 icon-arrow_01_down"></i>
                </div>
                <div v-if="dropdowns.propertyType" class="dropdown-menu">
                    <div
                        v-for="option in propertyTypeOptions"
                        :key="option.value"
                        class="dropdown-item"
                        @click="selectFilter('propertyType', option)"
                    >
                        {{ option.label }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'SearchFilter',
    props: {
        searchPlaceholder: {
            type: String,
            default: 'Search by Property Address, Holder, Key ID'
        }
    },
    data() {
        return {
            searchKeyword: '',
            dropdowns: {
                status: false,
                holder: false,
                propertyType: false
            },
            statusFilter: { value: 'all', label: 'All' },
            holderFilter: { value: 'all', label: 'All' },
            propertyTypeFilter: { value: 'all', label: 'All' },
            statusOptions: [
                { value: 'all', label: 'All' },
                { value: 'available', label: 'Available' },
                { value: 'checked_out', label: 'Checked Out' },
                { value: 'lost', label: 'Lost' },
                { value: 'damaged', label: 'Damaged' },
                { value: 'archived', label: 'Archived' }
            ],
            holderOptions: [
                { value: 'all', label: 'All' }
                // Holder options will be dynamically loaded via API
            ],
            propertyTypeOptions: [
                { value: 'all', label: 'All' },
                { value: 'for_sale', label: 'For Sale' },
                { value: 'to_let', label: 'To Let' }
            ]
        };
    },
    watch: {
        // Watch for route changes
        '$route'() {
            this.setPropertyTypeFromRoute();
        }
    },
    methods: {
        onSearchChange() {
            this.$emit('search-change', this.searchKeyword);
        },
        toggleDropdown(type) {
            // Close other dropdowns
            Object.keys(this.dropdowns).forEach(key => {
                if (key !== type) {
                    this.dropdowns[key] = false;
                }
            });
            // Toggle current dropdown
            this.dropdowns[type] = !this.dropdowns[type];
        },
        selectFilter(type, option) {
            if (type === 'status') {
                this.statusFilter = option;
            } else if (type === 'holder') {
                this.holderFilter = option;
            } else if (type === 'propertyType') {
                this.propertyTypeFilter = option;
            }

            this.dropdowns[type] = false;
            this.$emit('filter-change', {
                status: this.statusFilter.value,
                holder_id: this.holderFilter.value === 'all' ? '' : this.holderFilter.value,
                property_type: this.propertyTypeFilter.value
            });
        },
        /**
         * Set property type filter based on route parameter
         */
        setPropertyTypeFromRoute() {
            const routeType = this.$route.params.type;

            if (routeType === 'sales') {
                // For sales route, set to 'for_sale'
                const forSaleOption = this.propertyTypeOptions.find(option => option.value === 'for_sale');
                if (forSaleOption) {
                    this.propertyTypeFilter = forSaleOption;
                    this.emitFilterChange();
                }
            } else if (routeType === 'lettings') {
                // For lettings route, set to 'to_let'
                const toLetOption = this.propertyTypeOptions.find(option => option.value === 'to_let');
                if (toLetOption) {
                    this.propertyTypeFilter = toLetOption;
                    this.emitFilterChange();
                }
            }
        },
        /**
         * Emit filter change event
         */
        emitFilterChange() {
            this.$emit('filter-change', {
                status: this.statusFilter.value,
                holder_id: this.holderFilter.value === 'all' ? '' : this.holderFilter.value,
                property_type: this.propertyTypeFilter.value
            });
        }
    },
    mounted() {
        // Set initial property type based on route
        this.setPropertyTypeFromRoute();

        // Click outside to close dropdown
        document.addEventListener('click', (e) => {
            if (!this.$el.contains(e.target)) {
                Object.keys(this.dropdowns).forEach(key => {
                    this.dropdowns[key] = false;
                });
            }
        });
    }
};
</script>

<style scoped>
.search-filter {
    display: flex;
    align-items: stretch;
    gap: 10px;
    padding: 10px;
    background: #FFFFFF;
    border: 1px solid #EBECF1;
    border-radius: 6px 6px 0px 0px;
}

.filter-left {
    flex: 1;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    width: 300px;
    height: 30px;
    padding: 0 10px;
    background: #FFFFFF;
    border: 1px solid #C6C8D1;
    border-radius: 6px;
}

.search-input-wrapper .icon2017 {
    width: 14px;
    height: 14px;
    color: #C6C8D1;
    margin-right: 10px;
}

.search-input {
    flex: 1;
    border: none;
    outline: none;
    font-family: 'SF Pro';
    font-weight: 400;
    font-size: 14px;
    line-height: 1.43;
    color: #515666;
}

.search-input::placeholder {
    color: #C6C8D1;
}

.filter-right {
    display: flex;
    gap: 10px;
}

.filter-item {
    position: relative;
}

.filter-dropdown {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    background: rgba(255, 255, 255, 0);
    border-radius: 6px;
    cursor: pointer;
    white-space: nowrap;
}

.filter-dropdown:hover {
    background: #f5f5f5;
}

.filter-label,
.filter-value {
    font-family: 'SF Pro';
    font-weight: 510;
    font-size: 14px;
    line-height: 1.43;
    color: #515666;
}

.filter-dropdown .icon2017 {
    width: 12px;
    height: 12px;
    color: #A0A3AF;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 150px;
    background: #FFFFFF;
    border: 1px solid #EBECF1;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.dropdown-item {
    padding: 8px 12px;
    font-family: 'SF Pro';
    font-weight: 400;
    font-size: 14px;
    line-height: 1.43;
    color: #515666;
    cursor: pointer;
}

.dropdown-item:hover {
    background: #f5f5f5;
}

.dropdown-item:first-child {
    border-radius: 6px 6px 0 0;
}

.dropdown-item:last-child {
    border-radius: 0 0 6px 6px;
}
</style>
