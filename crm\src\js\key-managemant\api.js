import { crmUtils } from "crm";

/**
 * Key Management API Service
 * API interfaces for key management functionality
 */
export default {
    /**
     * Get key list
     * @param {Object} params - Query parameters
     * @param {number} params.page - Page number, default 1
     * @param {number} params.limit - Items per page, default 20, max 100
     * @param {string} params.status - Key status filter: available, checked_out, lost, damaged, archived
     * @param {string} params.holder_id - Key holder ID
     * @param {string} params.property_id - Property ID for querying keys, used in property detail page
     * @param {string} params.property_type - Property type filter: for_sale, to_let
     * @param {string} params.search - Search keyword (key holder name, property address)
     * @returns {Promise} API response
     */
    getKeyList(params = {}) {
        // Set default parameters
        const defaultParams = {
            page: 1,
            limit: 20
        };

        const queryParams = { ...defaultParams, ...params };

        return crmUtils.sendAjax({
            url: "/api/keys",
            type: "GET",
            data: queryParams
        }).then(response => {
            // Ensure returned data structure meets expectations
            if (response && response.data) {
                return {
                    success: response.status?.code === 200,
                    data: response.data.data || [],
                    total: response.data.total || 0,
                    page: queryParams.page,
                    limit: queryParams.limit
                };
            }
            return {
                success: false,
                data: [],
                total: 0,
                page: queryParams.page,
                limit: queryParams.limit
            };
        }).catch(error => {
            console.error('Failed to get key list:', error);
            return {
                success: false,
                data: [],
                total: 0,
                page: queryParams.page,
                limit: queryParams.limit,
                error: error.message || 'Failed to get key list'
            };
        });
    },

    /**
     * Get key detail
     * @param {string} keyId - Key ID
     * @returns {Promise} API response
     */
    getKeyDetail(keyId) {
        return crmUtils.sendAjax({
            url: `/api/keys/${keyId}`,
            type: "GET"
        }).then(response => {
            if (response && response.data) {
                return {
                    success: response.status?.code === 200,
                    data: response.data || {}
                };
            }
            return {
                success: false,
                data: {}
            };
        }).catch(error => {
            console.error('Failed to get key detail:', error);
            return {
                success: false,
                data: {},
                error: error.message || 'Failed to get key detail'
            };
        });
    },

    /**
     * Get key statistics
     * @param {Object} params - Query parameters
     * @param {string} params.status - Key status filter: available, checked_out, lost, damaged, archived
     * @param {string} params.holder_id - Key holder ID
     * @param {string} params.property_id - Property ID
     * @param {string} params.property_type - Property type filter: for_sale, to_let
     * @param {string} params.search - Search keyword (key holder name, property address)
     * @returns {Promise} API response
     */
    getKeyStatistics(params = {}) {
        return crmUtils.sendAjax({
            url: "/api/keys/statistics",
            type: "GET",
            data: params
        }).then(response => {
            if (response && response.data) {
                return {
                    success: response.status?.code === 200,
                    data: response.data || {}
                };
            }
            return {
                success: false,
                data: {}
            };
        }).catch(error => {
            console.error('Failed to get key statistics:', error);
            return {
                success: false,
                data: {},
                error: error.message || 'Failed to get key statistics'
            };
        });
    },

    /**
     * Update key status
     * @param {string} keyId - Key ID
     * @param {Object} updateData - Update data
     * @returns {Promise} API response
     */
    updateKeyStatus(keyId, updateData) {
        return crmUtils.sendAjax({
            url: `/api/keys/${keyId}`,
            type: "PUT",
            contentType: "application/json",
            data: JSON.stringify(updateData)
        }).then(response => {
            return {
                success: response.status?.code === 200,
                data: response.data || {}
            };
        }).catch(error => {
            console.error('Failed to update key status:', error);
            return {
                success: false,
                error: error.message || 'Failed to update key status'
            };
        });
    }
};
